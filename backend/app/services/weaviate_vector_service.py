"""
Weaviate Vector Database Service

Handles unlimited code indexing and retrieval using Weaviate.
Provides semantic search capabilities with no dimension or storage limits.
"""

import json
import logging
import asyncio
from typing import Dict, List, Any, Optional
from datetime import datetime
import weaviate
from ..config import settings

logger = logging.getLogger(__name__)


class WeaviateVectorService:
    """Service for managing unlimited code indexing in Weaviate"""
    
    def __init__(self):
        self.client = None
        self.schema_initialized = False
        self.max_batch_size = 100  # Optimal batch size for Weaviate
        
    async def __aenter__(self):
        """Async context manager entry"""
        await self.connect()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit"""
        if self.client:
            self.client.close()
    
    async def connect(self):
        """Connect to Weaviate instance"""
        try:
            # Connect to Weaviate using environment configuration
            weaviate_url = settings.weaviate_url
            
            self.client = weaviate.Client(
                url=weaviate_url,
                timeout_config=(5, 15),  # (connect, read) timeouts
            )
            
            # Test connection
            if not self.client.is_ready():
                raise Exception("Weaviate is not ready")
            
            # Initialize schema if needed
            await self._ensure_schema_exists()
            
            logger.info("Connected to Weaviate vector database successfully")
            
        except Exception as e:
            logger.error(f"Failed to connect to Weaviate: {str(e)}")
            raise Exception(f"Weaviate connection failed: {str(e)}")
    
    async def _ensure_schema_exists(self):
        """Ensure Weaviate schema exists for code storage"""
        try:
            # Check if CodeChunk class exists
            existing_schema = self.client.schema.get()
            class_names = [cls['class'] for cls in existing_schema.get('classes', [])]
            
            if 'CodeChunk' not in class_names:
                await self._create_schema()
            
            self.schema_initialized = True
            logger.info("Weaviate schema verified/created successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize Weaviate schema: {str(e)}")
            raise
    
    async def _create_schema(self):
        """Create Weaviate schema for code storage"""
        schema = {
            "classes": [
                {
                    "class": "CodeChunk",
                    "description": "A chunk of code from a repository with semantic embeddings",
                    "vectorizer": "none",  # We provide our own vectors
                    "properties": [
                        {
                            "name": "analysis_id",
                            "dataType": ["int"],
                            "description": "Analysis ID for grouping"
                        },
                        {
                            "name": "file_path",
                            "dataType": ["text"],
                            "description": "Path to the source file"
                        },
                        {
                            "name": "content",
                            "dataType": ["text"],
                            "description": "Code content"
                        },
                        {
                            "name": "language",
                            "dataType": ["text"],
                            "description": "Programming language"
                        },
                        {
                            "name": "function_name",
                            "dataType": ["text"],
                            "description": "Function name if applicable"
                        },
                        {
                            "name": "chunk_index",
                            "dataType": ["int"],
                            "description": "Index of chunk within file"
                        },
                        {
                            "name": "file_size",
                            "dataType": ["int"],
                            "description": "Size of source file in bytes"
                        },
                        {
                            "name": "created_at",
                            "dataType": ["date"],
                            "description": "When this chunk was indexed"
                        }
                    ]
                }
            ]
        }
        
        self.client.schema.create(schema)
        logger.info("Created Weaviate schema for CodeChunk class")
    
    async def index_repository_code(self, analysis_id: int, repo_content: Dict[str, Any]) -> Dict[str, Any]:
        """
        Index repository code into Weaviate with unlimited capacity
        
        Args:
            analysis_id: ID of the analysis
            repo_content: Repository content from analysis service
            
        Returns:
            Dict with indexing statistics
        """
        try:
            if not self.schema_initialized:
                await self._ensure_schema_exists()
            
            code_samples = repo_content.get("code_samples", {})
            if not code_samples:
                logger.warning(f"No code samples found for analysis {analysis_id}")
                return {"chunks_indexed": 0, "files_processed": 0}
            
            # Process all files in parallel batches
            all_chunks = []
            files_processed = 0
            
            # Create chunks from all files
            for file_path, content in code_samples.items():
                if content and isinstance(content, str):
                    chunks = await self._create_code_chunks(
                        analysis_id, file_path, content
                    )
                    all_chunks.extend(chunks)
                    files_processed += 1
            
            # Index chunks in parallel batches
            chunks_indexed = await self._batch_index_chunks(all_chunks)
            
            logger.info(f"Successfully indexed {chunks_indexed} chunks from {files_processed} files for analysis {analysis_id}")
            
            return {
                "chunks_indexed": chunks_indexed,
                "files_processed": files_processed,
                "analysis_id": analysis_id,
                "storage_type": "weaviate",
                "unlimited_capacity": True
            }
            
        except Exception as e:
            logger.error(f"Failed to index repository code in Weaviate: {str(e)}")
            raise Exception(f"Weaviate indexing failed: {str(e)}")
    
    async def _create_code_chunks(self, analysis_id: int, file_path: str, content: str) -> List[Dict[str, Any]]:
        """Create code chunks from file content"""
        chunks = []
        
        # Determine language from file extension
        language = self._detect_language(file_path)
        
        # Split content into manageable chunks (no size limits!)
        chunk_size = 2000  # Characters per chunk
        overlap = 200  # Overlap between chunks
        
        for i in range(0, len(content), chunk_size - overlap):
            chunk_content = content[i:i + chunk_size]
            
            if chunk_content.strip():  # Skip empty chunks
                chunks.append({
                    "analysis_id": analysis_id,
                    "file_path": file_path,
                    "content": chunk_content,
                    "language": language,
                    "chunk_index": len(chunks),
                    "file_size": len(content),
                    "created_at": datetime.utcnow().isoformat(),
                    "function_name": self._extract_function_name(chunk_content, language)
                })
        
        return chunks
    
    async def _batch_index_chunks(self, chunks: List[Dict[str, Any]]) -> int:
        """Index chunks in parallel batches for maximum performance"""
        if not chunks:
            return 0
        
        # Process in batches for optimal performance
        total_indexed = 0
        batch_tasks = []
        
        for i in range(0, len(chunks), self.max_batch_size):
            batch = chunks[i:i + self.max_batch_size]
            batch_tasks.append(self._index_batch(batch))
        
        # Execute all batches in parallel
        batch_results = await asyncio.gather(*batch_tasks, return_exceptions=True)
        
        for result in batch_results:
            if isinstance(result, Exception):
                logger.error(f"Batch indexing failed: {result}")
            else:
                total_indexed += result
        
        return total_indexed
    
    async def _index_batch(self, batch: List[Dict[str, Any]]) -> int:
        """Index a single batch of chunks"""
        try:
            # Use Weaviate batch API for optimal performance
            with self.client.batch as batch_client:
                batch_client.batch_size = len(batch)
                
                for chunk in batch:
                    # Add object without vector (we'll add vectors separately if needed)
                    batch_client.add_data_object(
                        data_object=chunk,
                        class_name="CodeChunk"
                    )
            
            return len(batch)
            
        except Exception as e:
            logger.error(f"Failed to index batch: {str(e)}")
            return 0
    
    def _detect_language(self, file_path: str) -> str:
        """Detect programming language from file extension"""
        extension_map = {
            '.py': 'python',
            '.js': 'javascript',
            '.ts': 'typescript',
            '.java': 'java',
            '.go': 'go',
            '.rs': 'rust',
            '.cpp': 'cpp',
            '.c': 'c',
            '.php': 'php',
            '.rb': 'ruby',
            '.swift': 'swift',
            '.kt': 'kotlin',
            '.scala': 'scala',
            '.sh': 'bash',
            '.sql': 'sql',
            '.yaml': 'yaml',
            '.yml': 'yaml',
            '.json': 'json',
            '.xml': 'xml',
            '.html': 'html',
            '.css': 'css',
            '.md': 'markdown'
        }
        
        for ext, lang in extension_map.items():
            if file_path.lower().endswith(ext):
                return lang
        
        return 'unknown'
    
    def _extract_function_name(self, content: str, language: str) -> Optional[str]:
        """Extract function name from code chunk"""
        try:
            # Simple function name extraction (can be enhanced)
            if language == 'python':
                import re
                match = re.search(r'def\s+(\w+)\s*\(', content)
                return match.group(1) if match else None
            elif language in ['javascript', 'typescript']:
                import re
                match = re.search(r'function\s+(\w+)\s*\(|(\w+)\s*:\s*\(.*\)\s*=>', content)
                return match.group(1) or match.group(2) if match else None
            
            return None
        except:
            return None

    async def search_similar_code(self, query: str, analysis_id: Optional[int] = None, limit: int = 10) -> List[Dict[str, Any]]:
        """
        Search for similar code using semantic search

        Args:
            query: Search query
            analysis_id: Optional analysis ID to filter results
            limit: Maximum number of results

        Returns:
            List of similar code chunks
        """
        try:
            # Build GraphQL query for semantic search
            where_filter = {}
            if analysis_id:
                where_filter = {
                    "path": ["analysis_id"],
                    "operator": "Equal",
                    "valueInt": analysis_id
                }

            # Use Weaviate's semantic search capabilities
            query_result = (
                self.client.query
                .get("CodeChunk", ["analysis_id", "file_path", "content", "language", "function_name", "chunk_index"])
                .with_near_text({"concepts": [query]})
                .with_where(where_filter) if where_filter else
                self.client.query
                .get("CodeChunk", ["analysis_id", "file_path", "content", "language", "function_name", "chunk_index"])
                .with_near_text({"concepts": [query]})
            ).with_limit(limit).do()

            results = query_result.get("data", {}).get("Get", {}).get("CodeChunk", [])

            logger.info(f"Found {len(results)} similar code chunks for query: {query[:50]}...")
            return results

        except Exception as e:
            logger.error(f"Failed to search similar code: {str(e)}")
            return []

    async def get_repository_stats(self, analysis_id: int) -> Dict[str, Any]:
        """Get statistics for indexed repository"""
        try:
            # Count total chunks for this analysis
            count_result = (
                self.client.query
                .aggregate("CodeChunk")
                .with_where({
                    "path": ["analysis_id"],
                    "operator": "Equal",
                    "valueInt": analysis_id
                })
                .with_meta_count()
                .do()
            )

            total_chunks = count_result.get("data", {}).get("Aggregate", {}).get("CodeChunk", [{}])[0].get("meta", {}).get("count", 0)

            # Get language distribution
            language_result = (
                self.client.query
                .aggregate("CodeChunk")
                .with_where({
                    "path": ["analysis_id"],
                    "operator": "Equal",
                    "valueInt": analysis_id
                })
                .with_group_by(["language"])
                .with_meta_count()
                .do()
            )

            languages = {}
            for group in language_result.get("data", {}).get("Aggregate", {}).get("CodeChunk", []):
                lang = group.get("groupedBy", {}).get("value")
                count = group.get("meta", {}).get("count", 0)
                if lang:
                    languages[lang] = count

            return {
                "analysis_id": analysis_id,
                "total_chunks": total_chunks,
                "languages": languages,
                "storage_type": "weaviate",
                "unlimited_capacity": True
            }

        except Exception as e:
            logger.error(f"Failed to get repository stats: {str(e)}")
            return {"analysis_id": analysis_id, "total_chunks": 0, "languages": {}}

    async def delete_analysis_data(self, analysis_id: int) -> bool:
        """Delete all data for a specific analysis"""
        try:
            # Delete all chunks for this analysis
            self.client.batch.delete_objects(
                class_name="CodeChunk",
                where={
                    "path": ["analysis_id"],
                    "operator": "Equal",
                    "valueInt": analysis_id
                }
            )

            logger.info(f"Deleted all data for analysis {analysis_id}")
            return True

        except Exception as e:
            logger.error(f"Failed to delete analysis data: {str(e)}")
            return False

    async def health_check(self) -> Dict[str, Any]:
        """Check Weaviate health and connectivity"""
        try:
            if not self.client:
                return {"status": "disconnected", "ready": False}

            is_ready = self.client.is_ready()
            is_live = self.client.is_live()

            # Get cluster info
            cluster_info = {}
            try:
                cluster_info = self.client.cluster.get_nodes_status()
            except:
                cluster_info = {"nodes": "unknown"}

            return {
                "status": "connected" if is_ready else "not_ready",
                "ready": is_ready,
                "live": is_live,
                "cluster_info": cluster_info,
                "schema_initialized": self.schema_initialized
            }

        except Exception as e:
            logger.error(f"Weaviate health check failed: {str(e)}")
            return {"status": "error", "error": str(e), "ready": False}
