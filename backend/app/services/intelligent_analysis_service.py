"""
Intelligent Analysis Service using AI (OpenAI/Claude) for comprehensive repository business logic analysis
"""
import json
import logging
import asyncio
import re
from typing import Dict, List, Any, Optional
from openai import Async<PERSON>penAI
import anthropic
from ..config import settings
from .github_service import GitHubService
from .integration_detector import IntegrationDetector
from .tavily_client import TavilyC<PERSON>
from .parallel_repository_processor import ParallelRepositoryProcessor
from .unified_vector_service import UnifiedVectorService


logger = logging.getLogger(__name__)


class IntelligentAnalysisService:
    """Service for AI-powered comprehensive repository analysis"""
    
    def __init__(self):
        # Initialize AI clients with timeouts for faster processing
        self.anthropic_client = anthropic.AsyncAnthropic(api_key=settings.anthropic_api_key, timeout=60.0) if settings.anthropic_api_key else None
        self.openai_client = AsyncOpenAI(api_key=settings.openai_api_key, timeout=60.0) if settings.openai_api_key else None
        self.github_service = GitHubService()

        # NEW: Unlimited processing services
        self.parallel_processor = ParallelRepositoryProcessor()
        self.vector_service = None  # Will be initialized when needed

        # Feature flags for gradual migration
        self.use_unlimited_processing = True  # Enable unlimited processing
        self.use_vector_storage = settings.weaviate_enabled

        # Performance settings for faster processing
        self.ai_timeout = 45.0  # 45 second timeout for AI calls
        self.max_concurrent_ai_calls = 3  # Limit concurrent AI calls

        # Legacy limits (kept for fallback scenarios)
        self.max_files_to_analyze = 500  # Fallback limit
        self.max_file_size = 1000000  # Fallback file size limit
        self.priority_file_size = 2000000  # Priority file size limit
        self.large_repo_threshold = 2000  # Large repo threshold
        self.large_repo_max_files = 300  # Large repo file limit

        # Determine which AI service to use
        self.use_claude = bool(settings.anthropic_api_key)
        logger.info(f"Initialized AI service: {'Claude' if self.use_claude else 'OpenAI' if self.openai_client else 'None'}")
        logger.info(f"Unlimited processing: {'Enabled' if self.use_unlimited_processing else 'Disabled'}")
        logger.info(f"Vector storage: {'Enabled' if self.use_vector_storage else 'Disabled'}")
    
    async def _call_ai_service(self, prompt: str, max_tokens: int = 4000) -> str:
        """Unified method to call either Claude or OpenAI with proper error handling"""
        try:
            if self.use_claude and self.anthropic_client:
                response = await self.anthropic_client.messages.create(
                    model="claude-3-5-sonnet-20241022",
                    max_tokens=max_tokens,
                    messages=[{"role": "user", "content": prompt}]
                )
                return response.content[0].text
            elif self.openai_client:
                response = await self.openai_client.chat.completions.create(
                    model="gpt-4o-mini",
                    messages=[{"role": "user", "content": prompt}],
                    temperature=0.3,
                    max_tokens=max_tokens
                )
                return response.choices[0].message.content
            else:
                raise ValueError("No AI service available (neither Claude nor OpenAI API key configured)")

        except Exception as e:
            error_msg = str(e).lower()

            # Check for specific error types
            if "insufficient_quota" in error_msg or "quota" in error_msg or "billing" in error_msg:
                raise Exception("AI_QUOTA_EXCEEDED: Your AI service quota has been exceeded. Please check your billing and usage limits.")
            elif "rate_limit" in error_msg or "rate limit" in error_msg:
                raise Exception("AI_RATE_LIMITED: AI service rate limit exceeded. Please try again in a few minutes.")
            elif "invalid_api_key" in error_msg or "unauthorized" in error_msg:
                raise Exception("AI_AUTH_ERROR: Invalid AI API key. Please check your configuration.")
            elif "model_not_found" in error_msg or "model" in error_msg:
                raise Exception("AI_MODEL_ERROR: AI model not available or not found.")
            else:
                raise Exception(f"AI_SERVICE_ERROR: {str(e)}")

    def _extract_json_from_response(self, content: str) -> Dict[str, Any]:
        """Robust JSON extraction from AI response that may contain extra text"""
        try:
            # First, try direct JSON parsing
            return json.loads(content.strip())
        except json.JSONDecodeError:
            pass

        # Try extracting from code blocks
        if '```json' in content:
            json_match = re.search(r'```json\s*(.*?)\s*```', content, re.DOTALL)
            if json_match:
                try:
                    return json.loads(json_match.group(1).strip())
                except json.JSONDecodeError:
                    pass

        # Try extracting from any code block
        if '```' in content:
            json_match = re.search(r'```[a-zA-Z]*\s*(.*?)\s*```', content, re.DOTALL)
            if json_match:
                try:
                    return json.loads(json_match.group(1).strip())
                except json.JSONDecodeError:
                    pass

        # Try finding JSON object boundaries
        json_match = re.search(r'\{.*\}', content, re.DOTALL)
        if json_match:
            try:
                return json.loads(json_match.group(0))
            except json.JSONDecodeError:
                pass

        # If all else fails, raise an error
        raise json.JSONDecodeError(f"Could not extract valid JSON from response: {content[:200]}...", content, 0)

    async def _analyze_everything_comprehensive(self, repo_content: Dict[str, Any]) -> Dict[str, Any]:
        """Single comprehensive analysis for Claude (large context window)"""
        
        # Create comprehensive prompt combining all analysis types
        prompt = f"""
Analyze this repository comprehensively for MCP (Model Context Protocol) server development potential.

Repository: {repo_content['repository_info']['name']}
Description: {repo_content['repository_info'].get('description', 'No description')}
Language: {repo_content['repository_info'].get('language', 'Unknown')}
Topics: {repo_content['repository_info'].get('topics', [])}

Repository Structure:
{json.dumps(repo_content.get('repository_tree', []), indent=2)[:5000]}

API Endpoints Detected:
{json.dumps(repo_content.get('api_endpoints', []), indent=2)[:2000]}

Code Files (key files):
{json.dumps({k: v for k, v in list(repo_content.get('code_samples', {}).items())[:10]}, indent=2)[:10000]}

Configuration Files:
{json.dumps(repo_content.get('config_files', {}), indent=2)[:3000]}

ANALYSIS INSTRUCTIONS:
1. First, understand the CORE BUSINESS LOGIC by analyzing the main code files
2. Identify the PRIMARY DOMAIN and what this repository actually does
3. Analyze the API ENDPOINTS to understand external interfaces
4. Examine CONFIGURATION FILES to understand dependencies and setup
5. Only then suggest MCP tools that would genuinely help AI assistants interact with this specific repository's functionality

Please provide a comprehensive analysis in this exact JSON format:

{{
    "business_logic": {{
        "primary_domain": "domain (e.g., 'document processing', 'web scraping', 'data analysis')",
        "business_purpose": "clear description of what this repository does",
        "core_operations": ["list", "of", "main", "business", "operations"],
        "business_entities": ["main", "data", "entities"],
        "business_rules": ["key", "business", "rules"],
        "target_users": ["types", "of", "users"],
        "use_cases": [
            {{
                "scenario": "business scenario",
                "user_story": "as a user, I want to...",
                "business_outcome": "expected result"
            }}
        ],
        "value_proposition": "core business value"
    }},
    "workflows": {{
        "data_flows": ["step1 -> step2 -> step3"],
        "business_processes": ["main", "business", "processes"],
        "integration_patterns": ["how", "it", "integrates"],
        "automation_opportunities": ["things", "that", "could", "be", "automated"]
    }},
    "api_capabilities": {{
        "existing_apis": [
            {{
                "endpoint": "/api/endpoint",
                "method": "GET/POST",
                "purpose": "what it does"
            }}
        ],
        "api_patterns": ["REST", "GraphQL", "etc"],
        "authentication": {{
            "methods": ["JWT", "OAuth", "etc"],
            "description": "auth approach"
        }},
        "external_integrations": ["external", "services", "used"],
        "potential_new_apis": [
            {{
                "endpoint": "/api/new-endpoint",
                "purpose": "potential new functionality",
                "business_value": "why it would be useful"
            }}
        ]
    }},
    "integration_opportunities": {{
        "current_integrations": ["existing", "integrations"],
        "data_management": {{
            "databases": ["database", "types"],
            "data_formats": ["JSON", "XML", "etc"],
            "storage_patterns": ["how", "data", "is", "stored"]
        }},
        "external_services": ["third", "party", "services"],
        "integration_opportunities": ["new", "integration", "possibilities"],
        "data_transformations": ["data", "transformation", "needs"]
    }},
    "mcp_suggestions": {{
        "categories": {{
            "CORE_BUSINESS_TOOLS": [
                {{
                    "name": "tool_name",
                    "description": "what it does",
                    "business_value": "business benefit",
                    "implementation_effort": "low/medium/high",
                    "use_cases": ["when", "to", "use", "it"]
                }},
                {{
                    "name": "another_core_tool",
                    "description": "another core business capability",
                    "business_value": "additional business value",
                    "implementation_effort": "low/medium/high",
                    "use_cases": ["more", "use", "cases"]
                }}
            ],
            "WORKFLOW_AUTOMATION": [
                {{
                    "name": "automation_tool",
                    "description": "workflow automation capability",
                    "business_value": "efficiency gain",
                    "implementation_effort": "low/medium/high",
                    "use_cases": ["automation", "scenarios"]
                }},
                {{
                    "name": "process_optimizer",
                    "description": "process optimization and orchestration",
                    "business_value": "streamlined workflows",
                    "implementation_effort": "low/medium/high",
                    "use_cases": ["process", "automation"]
                }}
            ],
            "DATA_INTEGRATION": [
                {{
                    "name": "data_tool",
                    "description": "data integration capability",
                    "business_value": "data value",
                    "implementation_effort": "low/medium/high",
                    "use_cases": ["data", "scenarios"]
                }},
                {{
                    "name": "sync_manager",
                    "description": "data synchronization and transformation",
                    "business_value": "unified data access",
                    "implementation_effort": "low/medium/high",
                    "use_cases": ["data", "sync", "transform"]
                }}
            ],
            "ANALYTICS_INTELLIGENCE": [
                {{
                    "name": "analytics_tool",
                    "description": "business intelligence and reporting",
                    "business_value": "data-driven insights",
                    "implementation_effort": "low/medium/high",
                    "use_cases": ["analytics", "reporting", "insights"]
                }},
                {{
                    "name": "metrics_dashboard",
                    "description": "performance metrics and KPI tracking",
                    "business_value": "operational visibility",
                    "implementation_effort": "low/medium/high",
                    "use_cases": ["metrics", "monitoring", "kpis"]
                }}
            ],
            "COMPLIANCE_SECURITY": [
                {{
                    "name": "security_tool",
                    "description": "security and compliance automation",
                    "business_value": "risk mitigation",
                    "implementation_effort": "low/medium/high",
                    "use_cases": ["security", "compliance", "audit"]
                }},
                {{
                    "name": "audit_tracker",
                    "description": "compliance monitoring and audit trails",
                    "business_value": "regulatory compliance",
                    "implementation_effort": "low/medium/high",
                    "use_cases": ["audit", "compliance", "tracking"]
                }}
            ],
            "OPTIMIZATION_PERFORMANCE": [
                {{
                    "name": "performance_tool",
                    "description": "performance optimization and monitoring",
                    "business_value": "improved efficiency",
                    "implementation_effort": "low/medium/high",
                    "use_cases": ["performance", "optimization", "monitoring"]
                }},
                {{
                    "name": "cost_optimizer",
                    "description": "cost analysis and optimization",
                    "business_value": "reduced operational costs",
                    "implementation_effort": "low/medium/high",
                    "use_cases": ["cost", "optimization", "analysis"]
                }}
            ],
            "USER_EXPERIENCE": [
                {{
                    "name": "ux_tool",
                    "description": "user experience and interface tools",
                    "business_value": "improved user satisfaction",
                    "implementation_effort": "low/medium/high",
                    "use_cases": ["user", "experience", "interface"]
                }},
                {{
                    "name": "interaction_helper",
                    "description": "user interaction and support tools",
                    "business_value": "enhanced user engagement",
                    "implementation_effort": "low/medium/high",
                    "use_cases": ["user", "support", "interaction"]
                }}
            ]
        }},
        "prioritized_recommendations": [
            {{
                "tool_name": "highest_priority_tool",
                "priority": "high",
                "reasoning": "why this is most important",
                "estimated_hours": 8
            }}
        ],
        "implementation_roadmap": {{
            "phase_1_quick_wins": ["tools", "that", "are", "easy", "to", "implement"],
            "phase_2_core_features": ["main", "functionality", "tools"],
            "phase_3_advanced": ["complex", "advanced", "tools"]
        }},
        "total_tools_suggested": 14,
        "estimated_total_effort_hours": 120
    }}
}}

CRITICAL REQUIREMENTS:
1. ONLY generate tools for categories that are ACTUALLY RELEVANT to this repository
2. Generate as many tools as make sense for each relevant category (could be 1, could be 10+)
3. Each tool MUST be directly based on actual functionality found in the repository
4. Each tool MUST solve a real problem that AI assistants would encounter when working with this repository
5. If a category has no relevant tools, leave it empty or omit it entirely
6. Tools should leverage specific APIs, functions, or capabilities found in the repository analysis
7. Validate that each suggested tool corresponds to actual repository functionality
"""
        
        try:
            content = await self._call_ai_service(prompt, max_tokens=8000)
            content = content.strip()
            
            # Extract JSON from response using robust method
            result = self._extract_json_from_response(content)
            logger.info("Comprehensive analysis completed successfully")
            return result
            
        except Exception as e:
            logger.error(f"Comprehensive analysis failed: {str(e)}")
            # Return empty structure if analysis fails
            return {
                "business_logic": {
                    "primary_domain": "unknown",
                    "business_purpose": "Analysis failed",
                    "core_operations": [],
                    "business_entities": [],
                    "business_rules": [],
                    "target_users": [],
                    "use_cases": [],
                    "value_proposition": "Could not determine"
                },
                "workflows": {
                    "data_flows": [],
                    "business_processes": [],
                    "integration_patterns": [],
                    "automation_opportunities": []
                },
                "api_capabilities": {
                    "existing_apis": [],
                    "api_patterns": [],
                    "authentication": {"methods": [], "description": ""},
                    "external_integrations": [],
                    "potential_new_apis": []
                },
                "integration_opportunities": {
                    "current_integrations": [],
                    "data_management": {"databases": [], "data_formats": [], "storage_patterns": []},
                    "external_services": [],
                    "integration_opportunities": [],
                    "data_transformations": []
                },
                "mcp_suggestions": {
                    "categories": {
                        "CORE_BUSINESS_TOOLS": [],
                        "WORKFLOW_AUTOMATION": [],
                        "DATA_INTEGRATION": [],
                        "ANALYTICS_INTELLIGENCE": [],
                        "COMPLIANCE_SECURITY": [],
                        "OPTIMIZATION_PERFORMANCE": [],
                        "USER_EXPERIENCE": []
                    },
                    "prioritized_recommendations": [],
                    "implementation_roadmap": {"phase_1_quick_wins": [], "phase_2_core_features": [], "phase_3_advanced": []},
                    "total_tools_suggested": 0,
                    "estimated_total_effort_hours": 0
                }
            }
        
    async def analyze_repository_comprehensively(
        self,
        repo_owner: str,
        repo_name: str,
        github_token: str,
        repo_info: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """
        Perform comprehensive business logic analysis with unlimited processing capability
        """
        try:
            repo_url = f"https://github.com/{repo_owner}/{repo_name}"
            logger.info(f"Starting comprehensive analysis for {repo_owner}/{repo_name}")

            # Try unlimited processing first
            if self.use_unlimited_processing:
                try:
                    logger.info(f"🚀 Using unlimited processing for {repo_url}")
                    return await self._analyze_repository_unlimited(repo_url, repo_owner, repo_name, github_token, repo_info)
                except Exception as e:
                    logger.warning(f"⚠️ Unlimited processing failed, falling back to legacy method: {e}")

            # Fallback to legacy GitHub API method
            logger.info(f"📊 Using legacy GitHub API analysis for {repo_url}")

            # 1. Gather comprehensive repository content (legacy method)
            repo_content = await self._gather_repository_content(
                repo_owner, repo_name, github_token, repo_info
            )
            
            # Add small delay to make progress visible
            await asyncio.sleep(1)

            # 2. Extract actual code implementations for MCP generation
            code_implementations = await self._extract_code_implementations(repo_content)

            # 3-7. Perform analysis (single call for Claude, multiple for OpenAI)
            if self.use_claude:
                # Use comprehensive single-call analysis for Claude
                comprehensive_results = await self._analyze_everything_comprehensive(repo_content)
                # Add delay to show AI processing time
                await asyncio.sleep(2)

                # Handle both direct and nested comprehensive analysis structure
                comp_analysis = comprehensive_results.get("comprehensive_analysis", comprehensive_results)
                business_analysis = comp_analysis.get("business_logic", {})
                workflow_analysis = comp_analysis.get("workflows", {})
                api_analysis = comp_analysis.get("api_capabilities", {})
                integration_analysis = comp_analysis.get("integration_opportunities", {})
                mcp_suggestions = comprehensive_results.get("mcp_suggestions", {})

                # Add extracted code implementations
                business_analysis["code_implementations"] = code_implementations
            else:
                # For OpenAI, use separate calls with delays (rate limiting)
                business_analysis = await self._analyze_business_logic(repo_content)
                await asyncio.sleep(22)  # Rate limit: 3 requests/minute = 20s between calls + buffer
                
                workflow_analysis = await self._analyze_workflows(repo_content)
                await asyncio.sleep(22)
                
                api_analysis = await self._analyze_api_capabilities(repo_content)
                await asyncio.sleep(22)
                
                integration_analysis = await self._analyze_integration_points(repo_content)
                await asyncio.sleep(22)
                
                # Optimize MCP generation by limiting code samples to prevent AI timeout
                code_samples_sample = dict(list(repo_content["code_samples"].items())[:20])

                mcp_suggestions = await self._generate_mcp_suggestions({
                    "repository_info": repo_content["repository_info"],
                    "business_logic": business_analysis,
                    "workflows": workflow_analysis,
                    "apis": api_analysis,
                    "integrations": integration_analysis,
                    "code_samples": code_samples_sample,  # Limited sample to prevent AI timeout
                    "total_files_processed": len(repo_content.get("code_samples", {}))
                })

                # Add extracted code implementations
                business_analysis["code_implementations"] = code_implementations
            
            # 7. Detect third-party integrations and find MCP alternatives
            integration_detector = IntegrationDetector()
            detected_integrations = await integration_detector.detect_integrations(repo_content)

            # 8. Find MCP alternatives for detected integrations (enhanced with marketplace search)
            analysis_data_for_alternatives = {
                "repository_info": repo_content["repository_info"],
                "business_logic": business_analysis,
                "workflows": workflow_analysis,
                "apis": api_analysis,
                "integrations": integration_analysis,
                "code_samples": repo_content["code_samples"]
            }
            mcp_alternatives = await integration_detector.find_mcp_alternatives(detected_integrations, analysis_data_for_alternatives)

            # 9. Calculate confidence and complexity scores
            confidence_score = self._calculate_analysis_confidence(mcp_suggestions)
            complexity_assessment = self._assess_implementation_complexity(mcp_suggestions)
            
            # Convert DetectedIntegration objects to dictionaries for JSON serialization
            serializable_integrations = []
            if detected_integrations:
                for integration in detected_integrations:
                    if hasattr(integration, 'to_dict'):
                        serializable_integrations.append(integration.to_dict())
                    elif isinstance(integration, dict):
                        serializable_integrations.append(integration)
                    else:
                        # Fallback for any other type
                        logger.warning(f"Unknown integration type: {type(integration)}")

            # Convert MCP alternatives to dictionaries
            serializable_alternatives = {}
            if mcp_alternatives:
                for key, alternatives in mcp_alternatives.items():
                    serializable_alternatives[key] = []
                    for alt in alternatives:
                        if hasattr(alt, 'to_dict'):
                            serializable_alternatives[key].append(alt.to_dict())
                        elif isinstance(alt, dict):
                            serializable_alternatives[key].append(alt)
                        else:
                            logger.warning(f"Unknown alternative type: {type(alt)}")

            return {
                "comprehensive_analysis": {
                    "business_logic": business_analysis,
                    "workflows": workflow_analysis,
                    "api_capabilities": api_analysis,
                    "integration_opportunities": integration_analysis
                },
                "mcp_suggestions": mcp_suggestions,
                "detected_integrations": serializable_integrations,
                "mcp_alternatives": serializable_alternatives,
                "confidence_score": confidence_score,
                "implementation_complexity": complexity_assessment,
                "analysis_metadata": {
                    "files_analyzed": repo_content.get("files_count", 0),
                    "analysis_timestamp": repo_content.get("analysis_timestamp"),
                    "repository_size": repo_content.get("repository_size", 0)
                },
                "repository_tree": repo_content.get("repository_tree", []),
                "repository_info": repo_content.get("repository_info", {})
            }
            
        except Exception as e:
            logger.error(f"Comprehensive analysis failed for {repo_owner}/{repo_name}: {str(e)}")
            raise Exception(f"AI-powered analysis failed: {str(e)}")
    
    async def _gather_repository_content(
        self, 
        repo_owner: str, 
        repo_name: str, 
        github_token: str,
        repo_info: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """Gather comprehensive repository content for analysis"""
        
        # Get basic repository info if not provided
        if not repo_info:
            repo_info = self.github_service.get_repository_info_sync(
                repo_owner, repo_name, github_token
            )

        # Get languages breakdown from GitHub API
        try:
            languages_data = self.github_service.get_repo_languages(
                github_token, repo_owner, repo_name
            )
            # Add languages data to repo_info
            repo_info['languages'] = languages_data

            # Calculate language percentages
            total_bytes = sum(languages_data.values())
            if total_bytes > 0:
                repo_info['language_percentages'] = {
                    lang: round((bytes_count / total_bytes) * 100, 1)
                    for lang, bytes_count in languages_data.items()
                }
        except Exception as e:
            logger.warning(f"Failed to fetch languages data: {str(e)}")
            repo_info['languages'] = {}
            repo_info['language_percentages'] = {}
        
        # Get repository tree and important files - increased depth for better coverage
        repo_tree = self.github_service.get_repository_tree_sync(
            repo_owner, repo_name, github_token, max_depth=6
        )
        
        # Identify key files to analyze
        important_files = self._identify_important_files(repo_tree)

        # Get file contents for analysis
        code_samples = {}
        api_endpoints = []
        config_files = {}
        files_analyzed = 0

        # Prioritize files for analysis
        priority_files = []
        regular_files = []

        for file_path in important_files:
            file_name = file_path.split('/')[-1].lower()
            # Priority patterns
            if any(pattern in file_name for pattern in [
                'main.', 'index.', 'app.', 'server.', 'config.', 'settings.',
                'readme', 'package.json', 'requirements.txt', 'cargo.toml'
            ]):
                priority_files.append(file_path)
            else:
                regular_files.append(file_path)

        # Adjust limits for large repositories
        total_files = len(important_files)
        is_large_repo = total_files > self.large_repo_threshold

        if is_large_repo:
            max_files = self.large_repo_max_files
            logger.info(f"Large repository detected ({total_files} files), reducing analysis to {max_files} most important files")
        else:
            max_files = self.max_files_to_analyze

        # Process priority files first, then regular files
        files_to_process = priority_files + regular_files[:max_files - len(priority_files)]

        for file_path in files_to_process[:max_files]:
            try:
                file_content = self.github_service.get_file_content_sync(
                    repo_owner, repo_name, file_path, github_token
                )

                # Use different size limits for priority vs regular files
                file_name = file_path.split('/')[-1].lower()
                is_priority = file_path in priority_files
                size_limit = self.priority_file_size if is_priority else self.max_file_size

                if file_content and len(file_content) <= size_limit:
                    # Truncate very large files but keep them
                    if len(file_content) > self.max_file_size:
                        file_content = file_content[:self.max_file_size] + "\n\n# [File truncated for analysis]"

                    code_samples[file_path] = file_content
                    files_analyzed += 1

                    # Extract API endpoints from code
                    if any(ext in file_path.lower() for ext in ['.py', '.js', '.ts', '.go', '.java']):
                        endpoints = self._extract_api_endpoints_from_code(file_content, file_path)
                        api_endpoints.extend(endpoints)

                    # Collect configuration files
                    if any(config in file_path.lower() for config in ['config', 'settings', '.env', 'package.json', 'requirements.txt', 'cargo.toml']):
                        config_files[file_path] = file_content[:2000]  # Limit config file size
                    
            except Exception as e:
                logger.warning(f"Failed to get content for {file_path}: {str(e)}")
                continue
        
        # Get nested tree structure for frontend display - aligned with analysis depth
        nested_tree = self.github_service.get_repository_tree_nested(
            repo_owner, repo_name, github_token, max_depth=6
        )
        
        return {
            "repository_info": repo_info,
            "repository_tree": nested_tree,
            "code_samples": code_samples,
            "api_endpoints": api_endpoints,
            "config_files": config_files,
            "files_count": files_analyzed,
            "repository_size": repo_info.get("size", 0),
            "analysis_timestamp": repo_info.get("updated_at")
        }
    
    def _identify_important_files(self, repo_tree: List[Dict]) -> List[str]:
        """Identify important files for business logic analysis"""
        
        important_patterns = [
            # Configuration and setup files
            "README.md", "setup.py", "pyproject.toml", "package.json", 
            "Cargo.toml", "go.mod", "pom.xml", "build.gradle",
            
            # Main application files
            "main.py", "app.py", "index.js", "server.js", "main.go",
            "__init__.py", "cli.py", "command.py",
            
            # API and service files
            "api/", "routes/", "handlers/", "controllers/", "services/",
            "endpoints/", "views/", "models/",
            
            # Documentation
            "docs/", "documentation/", "examples/", "CHANGELOG",
            
            # Configuration
            "config/", "settings/", "env", ".env.example"
        ]
        
        important_files = []
        
        for item in repo_tree:
            file_path = item.get("path", "")
            file_name = file_path.split("/")[-1]
            
            # Check if file matches important patterns
            for pattern in important_patterns:
                if (pattern in file_path.lower() or 
                    pattern in file_name.lower() or
                    file_path.startswith(pattern) or
                    file_name == pattern):
                    important_files.append(file_path)
                    break
        
        # Sort by importance (main files first, then config, then others)
        def file_importance(file_path):
            main_files = ["main.py", "app.py", "index.js", "server.js", "README.md"]
            if any(main in file_path.lower() for main in main_files):
                return 0
            elif "api" in file_path.lower() or "route" in file_path.lower():
                return 1
            elif "config" in file_path.lower() or "setup" in file_path.lower():
                return 2
            else:
                return 3
        
        important_files.sort(key=file_importance)
        return important_files

    def _extract_api_endpoints_from_code(self, file_content: str, file_path: str) -> List[Dict[str, Any]]:
        """Extract API endpoints from code content"""
        endpoints = []

        # Universal API endpoint patterns that work across ALL frameworks
        patterns = [
            # HTTP method decorators (Python, Java, etc.)
            r'@\w*\.(get|post|put|delete|patch)\(["\']([^"\']+)["\']',
            r'@(Get|Post|Put|Delete|Patch)Mapping\(["\']([^"\']+)["\']',

            # Route definitions (any framework)
            r'\.(get|post|put|delete|patch)\(["\']([^"\']+)["\']',
            r'route\(["\']([^"\']+)["\']',
            r'path\(["\']([^"\']+)["\']',

            # HTTP method patterns
            r'(GET|POST|PUT|DELETE|PATCH)\s+["\']([^"\']+)["\']',
            r'["\']([^"\']+)["\']\s*,\s*["\']?(GET|POST|PUT|DELETE|PATCH)["\']?',

            # Generic handler patterns
            r'HandleFunc\(["\']([^"\']+)["\']',
            r'handle\(["\']([^"\']+)["\']',

            # URL/endpoint patterns
            r'url\s*=\s*["\']([^"\']+)["\']',
            r'endpoint\s*=\s*["\']([^"\']+)["\']'
        ]

        for pattern in patterns:
            matches = re.finditer(pattern, file_content, re.IGNORECASE)
            for match in matches:
                if len(match.groups()) >= 2:
                    method = match.group(1).upper() if match.group(1) else 'GET'
                    path = match.group(2) if len(match.groups()) >= 2 else match.group(1)

                    endpoints.append({
                        "method": method,
                        "path": path,
                        "file": file_path,
                        "line_context": self._get_line_context(file_content, match.start())
                    })

        return endpoints

    def _get_line_context(self, content: str, position: int) -> str:
        """Get surrounding lines for context"""
        lines = content[:position].split('\n')
        line_num = len(lines)
        all_lines = content.split('\n')

        start = max(0, line_num - 2)
        end = min(len(all_lines), line_num + 3)

        return '\n'.join(all_lines[start:end])

    def _validate_repository_specific_tools(self, suggestions: Dict[str, Any], analysis_data: Dict[str, Any]) -> Dict[str, Any]:
        """Validate that suggested tools are actually relevant to the repository"""

        # Extract repository context for validation
        repo_info = analysis_data.get("repository_info", {})
        business_logic = analysis_data.get("business_logic", {})
        apis = analysis_data.get("apis", {})
        code_samples = analysis_data.get("code_samples", {})

        # Create validation context
        validation_context = {
            "repo_name": repo_info.get("name", "").lower(),
            "repo_description": repo_info.get("description", "").lower(),
            "languages": repo_info.get("language", "").lower(),
            "api_endpoints": [ep.get("path", "") for ep in apis.get("endpoints", [])],
            "business_functions": business_logic.get("core_functions", []),
            "code_content": " ".join(code_samples.values()).lower() if code_samples else ""
        }

        validated_suggestions = {"categories": {}}

        for category, tools in suggestions.get("categories", {}).items():
            validated_tools = []

            for tool in tools:
                if self._is_tool_repository_specific(tool, validation_context):
                    validated_tools.append(tool)
                else:
                    logger.warning(f"Filtered out non-repository-specific tool: {tool.get('tool_name', 'Unknown')}")

            # Only include categories with validated tools
            if validated_tools:
                validated_suggestions["categories"][category] = validated_tools

        # Copy other fields
        for key, value in suggestions.items():
            if key != "categories":
                validated_suggestions[key] = value

        return validated_suggestions

    def _is_tool_repository_specific(self, tool: Dict[str, Any], context: Dict[str, Any]) -> bool:
        """Check if a tool is specific to the repository"""

        tool_name = tool.get("tool_name", "").lower()
        business_value = tool.get("business_value", "").lower()
        dependencies = tool.get("dependencies", [])
        repository_evidence = tool.get("repository_evidence", "").lower()

        # Check for generic/non-specific tools
        generic_patterns = [
            "code_review", "documentation", "generic", "standard", "basic",
            "simple", "common", "general", "universal", "default"
        ]

        if any(pattern in tool_name for pattern in generic_patterns):
            return False

        # Check for repository-specific evidence
        specificity_indicators = [
            # Tool references specific repository components
            any(dep in context["code_content"] for dep in dependencies if dep),
            # Tool name relates to repository domain
            any(word in context["repo_name"] for word in tool_name.split("_") if len(word) > 3),
            # Business value mentions repository-specific functionality
            any(endpoint in business_value for endpoint in context["api_endpoints"] if endpoint),
            # Repository evidence is provided
            len(repository_evidence) > 10,
            # Tool relates to identified business functions
            any(func in tool_name for func in context["business_functions"] if func)
        ]

        # Tool must have at least one specificity indicator
        return any(specificity_indicators)
    
    async def _analyze_business_logic(self, repo_content: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze repository's core business logic"""
        
        prompt = f"""
        Analyze this repository's core business logic and purpose:

        Repository Info:
        - Name: {repo_content['repository_info'].get('name')}
        - Description: {repo_content['repository_info'].get('description')}
        - Language: {repo_content['repository_info'].get('language')}
        - Topics: {repo_content['repository_info'].get('topics', [])}

        Key Files Content:
        {json.dumps({k: v for k, v in list(repo_content['code_samples'].items())[:5]}, indent=2)}

        Please analyze and identify:
        1. Primary business domain (e.g., document processing, data analysis, web scraping, etc.)
        2. Core business operations and their purposes
        3. Key business entities and data models
        4. Business rules and logic patterns
        5. Value proposition - what business problem does this solve?
        6. Target users and use cases
        7. Business processes and workflows

        Return as JSON with this structure:
        {{
            "primary_domain": "string",
            "business_purpose": "detailed description",
            "core_operations": [
                {{
                    "name": "operation_name",
                    "purpose": "what it does for business",
                    "business_value": "why it matters",
                    "complexity": "low|medium|high"
                }}
            ],
            "business_entities": ["entity1", "entity2"],
            "business_rules": ["rule1", "rule2"],
            "target_users": ["user_type1", "user_type2"],
            "use_cases": [
                {{
                    "scenario": "business scenario",
                    "user_story": "as a user, I want to...",
                    "business_outcome": "expected result"
                }}
            ],
            "value_proposition": "core business value"
        }}
        """
        
        try:
            content = await self._call_ai_service(prompt, max_tokens=2000)
            content = content.strip()
            
            # Try to extract JSON from the response if it contains other text
            if content.startswith('```json'):
                content = content.split('```json')[1].split('```')[0].strip()
            elif content.startswith('```'):
                content = content.split('```')[1].split('```')[0].strip()
            
            return json.loads(content)
            
        except Exception as e:
            logger.error(f"Business logic analysis failed: {str(e)}")
            return {
                "primary_domain": "unknown",
                "business_purpose": "Analysis failed",
                "core_operations": [],
                "business_entities": [],
                "business_rules": [],
                "target_users": [],
                "use_cases": [],
                "value_proposition": "Could not determine"
            }
    
    async def _analyze_workflows(self, repo_content: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze workflow patterns and business processes"""
        
        prompt = f"""
        Analyze the workflow patterns and business processes in this repository:

        Code Samples:
        {json.dumps({k: v for k, v in list(repo_content['code_samples'].items())[:8]}, indent=2)}

        Identify:
        1. Data flow patterns (input → processing → output)
        2. Business process workflows
        3. Integration points with external systems
        4. Automation opportunities
        5. Decision points and business rules
        6. Error handling and recovery processes

        Return as JSON:
        {{
            "data_flows": [
                {{
                    "name": "flow_name",
                    "description": "what the flow does",
                    "inputs": ["input1", "input2"],
                    "processing_steps": ["step1", "step2"],
                    "outputs": ["output1", "output2"],
                    "business_impact": "why this flow matters"
                }}
            ],
            "business_processes": [
                {{
                    "process_name": "name",
                    "description": "what it accomplishes",
                    "steps": ["step1", "step2"],
                    "automation_level": "manual|semi-automated|automated",
                    "improvement_opportunities": ["opportunity1"]
                }}
            ],
            "integration_patterns": [
                {{
                    "integration_type": "API|Database|File|etc",
                    "description": "what it integrates with",
                    "data_exchanged": ["data_type1"],
                    "frequency": "real-time|batch|on-demand"
                }}
            ],
            "automation_opportunities": [
                {{
                    "opportunity": "what could be automated",
                    "current_state": "how it works now",
                    "potential_benefit": "business value of automation",
                    "complexity": "low|medium|high"
                }}
            ]
        }}
        """
        
        try:
            content = await self._call_ai_service(prompt, max_tokens=2000)
            content = content.strip()
            
            # Try to extract JSON from the response if it contains other text
            if content.startswith('```json'):
                content = content.split('```json')[1].split('```')[0].strip()
            elif content.startswith('```'):
                content = content.split('```')[1].split('```')[0].strip()
            
            return json.loads(content)
            
        except Exception as e:
            logger.error(f"Workflow analysis failed: {str(e)}")
            return {
                "data_flows": [],
                "business_processes": [],
                "integration_patterns": [],
                "automation_opportunities": []
            }
    
    async def _analyze_api_capabilities(self, repo_content: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze API patterns and capabilities"""
        
        prompt = f"""
        Analyze the API capabilities and patterns in this repository:

        Code Samples:
        {json.dumps({k: v for k, v in list(repo_content['code_samples'].items())[:10]}, indent=2)}

        Identify:
        1. Existing API endpoints and their purposes
        2. API design patterns used
        3. Authentication and authorization methods
        4. Data formats and schemas
        5. Rate limiting and performance considerations
        6. External API integrations
        7. Potential new API endpoints that could be created

        Return as JSON:
        {{
            "existing_apis": [
                {{
                    "endpoint": "/api/endpoint",
                    "method": "GET|POST|PUT|DELETE",
                    "purpose": "what it does",
                    "input_schema": {{"param": "type"}},
                    "output_schema": {{"result": "type"}},
                    "business_function": "business purpose"
                }}
            ],
            "api_patterns": [
                {{
                    "pattern_name": "REST|GraphQL|RPC|etc",
                    "usage": "how it's used",
                    "benefits": "advantages for business"
                }}
            ],
            "authentication": {{
                "methods": ["API_KEY", "OAuth", "JWT"],
                "description": "how auth works"
            }},
            "external_integrations": [
                {{
                    "service": "external service name",
                    "purpose": "why it's integrated",
                    "api_type": "REST|GraphQL|etc",
                    "data_exchanged": ["data_type1"]
                }}
            ],
            "potential_new_apis": [
                {{
                    "proposed_endpoint": "/api/new-endpoint",
                    "purpose": "business need it would serve",
                    "value_proposition": "why create this API",
                    "complexity": "low|medium|high"
                }}
            ]
        }}
        """
        
        try:
            content = await self._call_ai_service(prompt, max_tokens=2000)
            content = content.strip()
            
            # Try to extract JSON from the response if it contains other text
            if content.startswith('```json'):
                content = content.split('```json')[1].split('```')[0].strip()
            elif content.startswith('```'):
                content = content.split('```')[1].split('```')[0].strip()
            
            return json.loads(content)
            
        except Exception as e:
            logger.error(f"API analysis failed: {str(e)}")
            return {
                "existing_apis": [],
                "api_patterns": [],
                "authentication": {"methods": [], "description": ""},
                "external_integrations": [],
                "potential_new_apis": []
            }
    
    async def _analyze_integration_points(self, repo_content: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze integration opportunities and system connections"""
        
        prompt = f"""
        Analyze integration opportunities and system connection points:

        Repository: {repo_content['repository_info'].get('name')}
        Code Analysis:
        {json.dumps({k: v for k, v in list(repo_content['code_samples'].items())[:8]}, indent=2)}

        Identify:
        1. Current system integrations
        2. Database connections and data management
        3. File system operations
        4. Network communications
        5. Third-party service integrations
        6. Potential new integration opportunities
        7. Data transformation and ETL processes

        Return as JSON:
        {{
            "current_integrations": [
                {{
                    "integration_name": "name",
                    "type": "Database|API|File|Service",
                    "purpose": "business reason",
                    "data_flow": "bidirectional|input|output",
                    "criticality": "high|medium|low"
                }}
            ],
            "data_management": {{
                "databases": ["db_type1", "db_type2"],
                "data_formats": ["JSON", "CSV", "XML"],
                "storage_patterns": ["pattern1", "pattern2"]
            }},
            "external_services": [
                {{
                    "service": "service_name",
                    "purpose": "why it's used",
                    "integration_method": "API|SDK|Direct",
                    "business_dependency": "high|medium|low"
                }}
            ],
            "integration_opportunities": [
                {{
                    "opportunity": "potential integration",
                    "business_value": "what business value it would provide",
                    "technical_approach": "how to implement",
                    "effort_required": "low|medium|high",
                    "roi_potential": "high|medium|low"
                }}
            ],
            "data_transformations": [
                {{
                    "transformation": "what data is transformed",
                    "from_format": "source format",
                    "to_format": "target format",
                    "business_purpose": "why transformation is needed"
                }}
            ]
        }}
        """
        
        try:
            content = await self._call_ai_service(prompt, max_tokens=2000)
            content = content.strip()
            
            # Try to extract JSON from the response if it contains other text
            if content.startswith('```json'):
                content = content.split('```json')[1].split('```')[0].strip()
            elif content.startswith('```'):
                content = content.split('```')[1].split('```')[0].strip()
            
            return json.loads(content)
            
        except Exception as e:
            logger.error(f"Integration analysis failed: {str(e)}")
            return {
                "current_integrations": [],
                "data_management": {"databases": [], "data_formats": [], "storage_patterns": []},
                "external_services": [],
                "integration_opportunities": [],
                "data_transformations": []
            }
    
    async def _generate_mcp_suggestions(self, analysis_data: Dict[str, Any]) -> Dict[str, Any]:
        """Generate categorized MCP tool suggestions based on comprehensive analysis"""
        
        prompt = f"""
        Context: Analyze this specific codebase to recommend ACTIONABLE MCP servers that would provide immediate value. Focus on what this codebase actually does, not generic categories.

        REPOSITORY ANALYSIS:
        Repository: {analysis_data.get('repository_info', {}).get('name', 'Unknown')}
        Description: {analysis_data.get('repository_info', {}).get('description', 'No description')}
        Primary Language: {analysis_data.get('repository_info', {}).get('language', 'Unknown')}

        ACTUAL BUSINESS LOGIC FOUND:
        {self._summarize_analysis_data(analysis_data.get('business_logic', {}), max_length=2000)}

        API ENDPOINTS FOUND:
        {self._summarize_analysis_data(analysis_data.get('apis', {}), max_length=1000)}

        INTEGRATION POINTS:
        {self._summarize_analysis_data(analysis_data.get('integrations', {}), max_length=1000)}

        KEY CODE SAMPLES (Sample of {len(analysis_data.get('code_samples', {}))} files, showing top 10):
        {json.dumps(list(analysis_data.get('code_samples', {}).keys())[:10], indent=2)}

        TOTAL FILES PROCESSED: {analysis_data.get('total_files_processed', 'Unknown')}

        EXTRACTED CODE IMPLEMENTATIONS:
        {self._summarize_analysis_data(analysis_data.get('business_logic', {}).get('code_implementations', {}), max_length=1500)}

        Your Task:
        1. Codebase Opportunity Analysis
        - Identify data sources (databases, APIs, file systems, external services)
        - Map business logic workflows and domain-specific calculations
        - Document integration points and current tool usage patterns
        - Highlight manual processes and context-switching pain points

        2. MCP Marketplace Intelligence
        Based on these proven marketplaces, recommend existing servers we can leverage immediately:
        - mcpmarket.com - Enterprise curated servers
        - mcp.so - Community collection (500+ servers)
        - github.com/modelcontextprotocol/servers - Official repository
        - mcpserverdirectory.org - Comprehensive index
        - cline.bot/mcp-marketplace - IDE-integrated options

        3. Use Case Prioritization
        For each opportunity, provide:
        - Current State: How this is handled now
        - MCP Enhancement: What the server would enable
        - Business Impact: Specific productivity/efficiency gains
        - Implementation Effort: Timeline and complexity assessment
        - Existing Solutions: Available marketplace servers vs. custom development needed

        4. Strategic Recommendations
        DO NOT use generic categories. Instead, provide SPECIFIC recommendations based on what this codebase actually does.

        Examples of GOOD recommendations:
        - "Repository Analysis MCP" (if this analyzes code repositories)
        - "Database Query MCP" (if database operations are found)
        - "API Testing MCP" (if API endpoints are detected)
        - "File Processing MCP" (if file operations are found)

        Examples of BAD recommendations:
        - "CORE_BUSINESS_TOOLS" (too generic)
        - "DATA_INTEGRATION" (too vague)
        - "WORKFLOW_AUTOMATION" (not specific)

        Focus on what THIS specific codebase does and how MCP servers could enhance those specific capabilities.

        IMPORTANT: For each recommendation, link it to specific extracted code:
        - Reference actual function names from the extracted code implementations
        - Specify which files contain the relevant business logic
        - Connect recommendations to real API endpoints, classes, or utilities found in the code

        Return as valid JSON with SPECIFIC recommendations based on THIS codebase:
        {{
            "specific_recommendations": [
                {{
                    "server_name": "Specific descriptive name based on what this codebase does",
                    "description": "What specific functionality from THIS codebase it would expose",
                    "current_functionality": "How this is currently handled in the codebase",
                    "mcp_enhancement": "What the MCP server would enable",
                    "implementation_type": "existing_server|custom_development",
                    "existing_mcp_server": "Name of existing server from github.com/modelcontextprotocol/servers if available",
                    "marketplace_url": "URL to existing server if available",
                    "estimated_effort": "1-week|2-3 weeks|4-6 weeks",
                    "business_value": "Specific productivity/efficiency gains",
                    "technical_requirements": ["specific", "requirements", "for", "this", "server"],
                    "source_functions": ["list", "of", "function", "names", "from", "codebase"],
                    "source_files": ["list", "of", "relevant", "files"]
                }}
            ],
            "codebase_analysis": {{
                "primary_functionality": "What this codebase primarily does",
                "key_data_sources": ["database", "apis", "files"],
                "main_workflows": ["workflow1", "workflow2"],
                "integration_points": ["service1", "service2"]
            }},
            "total_tools_suggested": 0,
            "estimated_total_effort_hours": 0
        }}
        """
        
        try:
            content = await self._call_ai_service(prompt, max_tokens=4000)
            content = content.strip()
            
            # Try to extract JSON from the response if it contains other text
            # Extract JSON from response using robust method
            suggestions = self._extract_json_from_response(content)
            
            # Calculate totals for specific recommendations
            total_tools = len(suggestions.get("specific_recommendations", []))
            total_effort = total_tools * 40  # Estimate 40 hours per recommendation

            suggestions["total_tools_suggested"] = total_tools
            suggestions["estimated_total_effort_hours"] = total_effort

            return suggestions
            
        except Exception as e:
            error_msg = str(e)
            logger.error(f"MCP suggestions generation failed: {error_msg}")

            # Check for specific AI service errors
            if error_msg.startswith("AI_QUOTA_EXCEEDED"):
                raise Exception("AI service quota exceeded. Please check your billing and try again later.")
            elif error_msg.startswith("AI_RATE_LIMITED"):
                raise Exception("AI service rate limited. Please wait a few minutes and try again.")
            elif error_msg.startswith("AI_AUTH_ERROR"):
                raise Exception("AI service authentication failed. Please check your API key configuration.")
            elif error_msg.startswith("AI_MODEL_ERROR"):
                raise Exception("AI model not available. Please try again later.")
            else:
                raise Exception(f"Failed to generate MCP suggestions: {error_msg}")
    
    def _calculate_strategic_effort(self, suggestions: Dict[str, Any]) -> int:
        """Calculate total effort hours from strategic recommendations"""
        total_hours = 0

        # Quick wins (1 week each = 40 hours)
        quick_wins = suggestions.get("marketplace_recommendations", {}).get("quick_wins", [])
        total_hours += len(quick_wins) * 40

        # Immediate wins (1 week each = 40 hours)
        immediate_wins = suggestions.get("strategic_recommendations", {}).get("immediate_wins", [])
        total_hours += len(immediate_wins) * 40

        # Custom development (2-4 weeks each = 80-160 hours, average 120)
        custom_dev = suggestions.get("strategic_recommendations", {}).get("custom_development", [])
        total_hours += len(custom_dev) * 120

        return total_hours

    def _calculate_total_effort(self, suggestions: Dict[str, Any]) -> int:
        """Legacy method for backward compatibility"""
        total_hours = 0

        for phase_tools in suggestions.get("implementation_roadmap", {}).values():
            for tool in phase_tools:
                total_hours += tool.get("effort_hours", 0)
                
        return total_hours
    
    def _calculate_analysis_confidence(self, mcp_suggestions: Dict[str, Any]) -> float:
        """Calculate confidence score for the analysis"""
        
        # Base confidence on number of tools suggested and roadmap completeness
        total_tools = mcp_suggestions.get("total_tools_suggested", 0)
        has_roadmap = bool(mcp_suggestions.get("implementation_roadmap", {}).get("phase_1_quick_wins"))
        has_priorities = bool(mcp_suggestions.get("prioritized_recommendations"))
        
        confidence = 0.0
        
        if total_tools > 0:
            confidence += min(total_tools * 0.05, 0.4)  # Up to 40% for having tools
        
        if has_roadmap:
            confidence += 0.3  # 30% for having implementation roadmap
            
        if has_priorities:
            confidence += 0.3  # 30% for having prioritized recommendations
            
        return min(confidence, 1.0)
    
    def _assess_implementation_complexity(self, mcp_suggestions: Dict[str, Any]) -> Dict[str, Any]:
        """Assess overall implementation complexity"""
        
        roadmap = mcp_suggestions.get("implementation_roadmap", {})
        total_effort = mcp_suggestions.get("estimated_total_effort_hours", 0)
        
        # Determine complexity level
        if total_effort <= 40:
            complexity_level = "low"
        elif total_effort <= 120:
            complexity_level = "medium"
        else:
            complexity_level = "high"
        
        return {
            "overall_complexity": complexity_level,
            "total_estimated_hours": total_effort,
            "quick_wins_available": len(roadmap.get("phase_1_quick_wins", [])),
            "advanced_features_count": len(roadmap.get("phase_3_advanced", [])),
            "recommendation": self._get_complexity_recommendation(complexity_level, total_effort)
        }
    
    def _get_complexity_recommendation(self, complexity_level: str, total_effort: int) -> str:
        """Get recommendation based on complexity assessment"""

        if complexity_level == "low":
            return f"Great candidate for MCP server development. Estimated {total_effort} hours for full implementation."
        elif complexity_level == "medium":
            return f"Good MCP server candidate. Consider phased implementation over {total_effort} hours."
        else:
            return f"Complex but valuable MCP server. Recommend starting with quick wins, full implementation ~{total_effort} hours."

    async def _find_mcp_alternatives(self, detected_integrations: List[Any]) -> Dict[str, Any]:
        """Find MCP alternatives for detected integrations using Tavily"""
        try:
            mcp_alternatives = {}

            # Use Tavily for web search
            async with TavilyClient() as tavily:
                for integration in detected_integrations:
                    integration_key = f"{integration.integration_type}_{integration.service_name}"

                    # Search for MCP alternatives
                    tavily_results = await tavily.search_mcp_alternatives(
                        integration.integration_type,
                        integration.service_name
                    )

                    mcp_alternatives[integration_key] = {
                        "integration": {
                            "type": integration.integration_type,
                            "service": integration.service_name,
                            "confidence": integration.confidence,
                            "complexity": integration.migration_complexity,
                            "detection_method": integration.detection_method,
                            "file_locations": integration.file_locations
                        },
                        "tavily_results": [
                            {
                                "name": result.name,
                                "description": result.description,
                                "url": result.url,
                                "github_url": result.github_url,
                                "confidence_score": result.confidence_score,
                                "benefits": result.benefits,
                                "installation_complexity": result.installation_complexity
                            }
                            for result in tavily_results
                        ]
                    }

            # Note: Context7 integration removed - using enhanced marketplace intelligence instead

            logger.info(f"Found MCP alternatives for {len(mcp_alternatives)} integrations")
            return mcp_alternatives

        except Exception as e:
            logger.error(f"Error finding MCP alternatives: {str(e)}")
            return {}

    async def _analyze_repository_unlimited(self, repo_url: str, repo_owner: str, repo_name: str, github_token: str, repo_info: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        Unlimited repository analysis using local cloning and parallel processing
        """
        try:
            # Generate analysis ID for tracking
            import time
            analysis_id = int(time.time() * 1000)  # Millisecond timestamp as ID

            logger.info(f"🚀 Starting unlimited analysis {analysis_id} for {repo_url}")

            # Step 1: Get repository metadata from GitHub API (fast and accurate)
            logger.info(f"📊 Fetching repository metadata from GitHub API")
            github_repo_info = self.github_service.get_repository_info_sync(repo_owner, repo_name, github_token)

            # Step 2: Process repository files with unlimited capacity (local cloning)
            logger.info(f"🚀 Processing repository files with unlimited capacity")
            repo_content = await self.parallel_processor.process_repository_unlimited(repo_url, analysis_id)

            # Step 3: Get repository structure from GitHub API (for frontend display)
            logger.info(f"🌳 Fetching repository structure from GitHub API")
            nested_tree = self.github_service.get_repository_tree_nested(
                repo_owner, repo_name, github_token, max_depth=6
            )

            # Step 4: Combine GitHub metadata with unlimited file processing
            repo_content["repository_info"] = github_repo_info  # Use GitHub's accurate metadata
            repo_content["repository_info"]["nested_tree"] = nested_tree  # Add repository structure

            logger.info(f"📊 Processed {repo_content['files_count']} files (unlimited capacity)")

            # Step 2: Initialize unified vector service if needed (optional)
            try:
                if self.use_vector_storage and not self.vector_service:
                    logger.info(f"🔍 Initializing vector service for code indexing")
                    self.vector_service = UnifiedVectorService()
                    await self.vector_service.__aenter__()
            except Exception as e:
                logger.warning(f"⚠️ Vector service initialization failed, continuing without it: {e}")
                self.vector_service = None

            # Step 3: Index all code in vector database (unified) - optional
            if self.vector_service:
                try:
                    logger.info(f"🔍 Indexing {repo_content['files_count']} files in vector database")
                    vector_result = await self.vector_service.index_repository_code(analysis_id, repo_content)
                    logger.info(f"✅ Indexed {vector_result.get('chunks_indexed', 0)} code chunks")
                    repo_content["vector_indexing"] = vector_result
                except Exception as e:
                    logger.warning(f"⚠️ Vector indexing failed, continuing without it: {e}")
                    repo_content["vector_indexing"] = {"error": str(e)}
            else:
                logger.info(f"📝 Skipping vector indexing (service not available)")
                repo_content["vector_indexing"] = {"status": "skipped", "reason": "vector_service_unavailable"}

            # Step 4: Extract actual code implementations for MCP generation
            code_implementations = await self._extract_code_implementations(repo_content)

            # Step 5: Perform AI analysis with unlimited context
            if self.use_claude:
                # Use comprehensive single-call analysis for Claude
                comprehensive_results = await self._analyze_everything_comprehensive(repo_content)
                await asyncio.sleep(2)  # Show AI processing time

                comp_analysis = comprehensive_results.get("comprehensive_analysis", comprehensive_results)
                business_analysis = comp_analysis.get("business_logic", {})
                workflow_analysis = comp_analysis.get("workflows", {})
                api_analysis = comp_analysis.get("apis", {})
                integration_analysis = comp_analysis.get("integrations", {})

            else:
                # Use multiple calls for OpenAI
                business_analysis = await self._analyze_business_logic(repo_content)
                await asyncio.sleep(1)

                workflow_analysis = await self._analyze_workflow_patterns(repo_content)
                await asyncio.sleep(1)

                api_analysis = await self._analyze_api_capabilities(repo_content)
                await asyncio.sleep(1)

                integration_analysis = await self._analyze_integration_points(repo_content)
                await asyncio.sleep(1)

            # Step 6: Generate MCP suggestions with optimized context (avoid overwhelming AI)
            # Only pass a sample of code files to prevent timeout
            code_samples_sample = dict(list(repo_content["code_samples"].items())[:20])

            mcp_suggestions = await self._generate_mcp_suggestions({
                "repository_info": repo_content["repository_info"],
                "business_logic": business_analysis,
                "workflows": workflow_analysis,
                "apis": api_analysis,
                "integrations": integration_analysis,
                "code_samples": code_samples_sample,  # Limited sample to prevent AI timeout
                "total_files_processed": repo_content.get("files_count", 0)
            })

            # Add extracted code implementations
            business_analysis["code_implementations"] = code_implementations

            logger.info(f"✅ Completed unlimited analysis {analysis_id} for {repo_url}")

            return {
                "repository_info": repo_content["repository_info"],
                "business_logic": business_analysis,
                "workflows": workflow_analysis,
                "apis": api_analysis,
                "integrations": integration_analysis,
                "mcp_suggestions": mcp_suggestions,
                "analysis_metadata": {
                    "analysis_id": analysis_id,
                    "processing_method": "unlimited_local_clone",
                    "files_processed": repo_content["files_count"],
                    "total_files_discovered": repo_content.get("total_files_discovered", 0),
                    "unlimited_capacity": True,
                    "vector_indexing": repo_content.get("vector_indexing", {}),
                    "api_calls_used": 1  # Only for cloning
                }
            }

        except Exception as e:
            logger.error(f"❌ Unlimited analysis failed for {repo_url}: {e}")
            raise Exception(f"Unlimited repository analysis failed: {e}")

    def _summarize_analysis_data(self, data: Any, max_length: int = 2000) -> str:
        """
        Summarize analysis data to prevent overwhelming the AI with too much information
        """
        try:
            if not data:
                return "No data available"

            # Convert to JSON string
            json_str = json.dumps(data, indent=2)

            # If it's within the limit, return as-is
            if len(json_str) <= max_length:
                return json_str

            # If it's too long, create a summary
            if isinstance(data, dict):
                summary = {}
                for key, value in data.items():
                    if isinstance(value, (list, dict)):
                        if isinstance(value, list):
                            summary[key] = f"[{len(value)} items]" if value else "[]"
                        else:
                            summary[key] = f"{{...{len(value)} keys...}}" if value else "{}"
                    else:
                        # Keep simple values
                        value_str = str(value)
                        summary[key] = value_str[:100] + "..." if len(value_str) > 100 else value_str

                return json.dumps(summary, indent=2)

            elif isinstance(data, list):
                if len(data) <= 5:
                    return json.dumps(data, indent=2)
                else:
                    return json.dumps(data[:5] + [f"...and {len(data) - 5} more items"], indent=2)

            else:
                # For other types, truncate string representation
                str_data = str(data)
                return str_data[:max_length] + "..." if len(str_data) > max_length else str_data

        except Exception as e:
            logger.warning(f"Failed to summarize analysis data: {e}")
            return f"Data summary unavailable: {type(data).__name__}"

    async def _extract_code_implementations(self, repo_content: Dict[str, Any]) -> Dict[str, Any]:
        """Extract actual code implementations that can be used for MCP generation"""

        code_implementations = {
            "functions": [],
            "classes": [],
            "api_endpoints": [],
            "utilities": [],
            "workflows": []
        }

        try:
            # Analyze code samples to extract function signatures and implementations
            code_samples = repo_content.get("code_samples", {})

            for file_path, content in code_samples.items():
                if not content or len(content.strip()) == 0:
                    continue

                # Extract based on file type
                file_ext = file_path.split('.')[-1].lower() if '.' in file_path else ''

                if file_ext in ['py', 'python']:
                    extracted = self._extract_python_implementations(file_path, content)
                elif file_ext in ['js', 'ts', 'javascript', 'typescript']:
                    extracted = self._extract_javascript_implementations(file_path, content)
                elif file_ext in ['go']:
                    extracted = self._extract_go_implementations(file_path, content)
                elif file_ext in ['java']:
                    extracted = self._extract_java_implementations(file_path, content)
                else:
                    # Generic extraction for other languages
                    extracted = self._extract_generic_implementations(file_path, content)

                # Merge extracted implementations
                for key in code_implementations:
                    if key in extracted:
                        code_implementations[key].extend(extracted[key])

            # Limit to most relevant implementations (top 20 per category)
            for key in code_implementations:
                code_implementations[key] = code_implementations[key][:20]

            logger.info(f"Extracted {sum(len(v) for v in code_implementations.values())} code implementations")

        except Exception as e:
            logger.error(f"Failed to extract code implementations: {str(e)}")

        return code_implementations

    def _extract_python_implementations(self, file_path: str, content: str) -> Dict[str, List[Dict[str, Any]]]:
        """Extract Python function and class implementations"""
        implementations = {"functions": [], "classes": [], "api_endpoints": [], "utilities": []}

        try:
            lines = content.split('\n')

            for i, line in enumerate(lines):
                stripped = line.strip()

                # Skip empty lines and comments
                if not stripped or stripped.startswith('#'):
                    continue

                # Function definition
                if stripped.startswith('def ') and ':' in stripped:
                    func_name = stripped.split('def ')[1].split('(')[0].strip()
                    params_part = stripped.split('(', 1)[1].split(')')[0] if '(' in stripped else ''

                    # Check for API endpoint decorators in previous lines
                    is_api_endpoint = False
                    for j in range(max(0, i-5), i):
                        if j < len(lines):
                            prev_line = lines[j].strip()
                            if any(keyword in prev_line.lower() for keyword in ['@app.route', '@router.', '@bp.route', '@api.route']):
                                is_api_endpoint = True
                                break

                    # Also check function content for API patterns
                    if not is_api_endpoint:
                        is_api_endpoint = any(keyword in stripped.lower() for keyword in ['request', 'response', 'jsonify'])

                    # Extract function body (next 10 lines or until next function/class)
                    body_lines = []
                    for j in range(i + 1, min(i + 15, len(lines))):
                        if j < len(lines) and lines[j].strip():
                            if not lines[j].startswith(' ') and not lines[j].startswith('\t') and (lines[j].strip().startswith('def ') or lines[j].strip().startswith('class ')):
                                break
                            body_lines.append(lines[j])
                            # Check body for API patterns
                            if not is_api_endpoint and any(keyword in lines[j].lower() for keyword in ['request.get_json', 'return jsonify', 'request.form']):
                                is_api_endpoint = True

                    func_impl = {
                        "name": func_name,
                        "file": file_path,
                        "line": i + 1,
                        "signature": stripped,
                        "parameters": params_part,
                        "body_preview": '\n'.join(body_lines[:10]),
                        "is_api_endpoint": is_api_endpoint,
                        "is_utility": func_name.startswith('_') or any(keyword in func_name.lower() for keyword in ['util', 'helper', 'format', 'parse', 'validate'])
                    }

                    if func_impl["is_api_endpoint"]:
                        implementations["api_endpoints"].append(func_impl)
                    elif func_impl["is_utility"]:
                        implementations["utilities"].append(func_impl)
                    else:
                        implementations["functions"].append(func_impl)

                # Class definition
                elif stripped.startswith('class ') and ':' in stripped:
                    class_name = stripped.split('class ')[1].split('(')[0].split(':')[0].strip()

                    # Extract class methods (next 20 lines)
                    methods = []
                    for j in range(i + 1, min(i + 25, len(lines))):
                        if j < len(lines):
                            method_line = lines[j].strip()
                            if method_line.startswith('def ') and ':' in method_line:
                                method_name = method_line.split('def ')[1].split('(')[0].strip()
                                methods.append(method_name)

                    class_impl = {
                        "name": class_name,
                        "file": file_path,
                        "line": i + 1,
                        "signature": stripped,
                        "methods": methods[:10],  # Limit to first 10 methods
                        "is_service": any(keyword in class_name.lower() for keyword in ['service', 'manager', 'handler', 'controller']),
                        "is_model": any(keyword in class_name.lower() for keyword in ['model', 'entity', 'schema'])
                    }

                    implementations["classes"].append(class_impl)

        except Exception as e:
            logger.warning(f"Failed to extract Python implementations from {file_path}: {str(e)}")

        return implementations

    def _extract_javascript_implementations(self, file_path: str, content: str) -> Dict[str, List[Dict[str, Any]]]:
        """Extract JavaScript/TypeScript function implementations"""
        implementations = {"functions": [], "classes": [], "api_endpoints": [], "utilities": []}

        try:
            lines = content.split('\n')

            for i, line in enumerate(lines):
                stripped = line.strip()

                # Function definitions
                if any(pattern in stripped for pattern in ['function ', 'const ', 'let ', 'var ']) and ('=' in stripped or '{' in stripped):
                    # Extract function name
                    func_name = ""
                    if 'function ' in stripped:
                        func_name = stripped.split('function ')[1].split('(')[0].strip()
                    elif '=' in stripped and ('function' in stripped or '=>' in stripped):
                        func_name = stripped.split('=')[0].strip().split(' ')[-1]

                    if func_name and not func_name.startswith('//'):
                        # Extract function body preview
                        body_lines = []
                        for j in range(i + 1, min(i + 10, len(lines))):
                            if j < len(lines):
                                body_lines.append(lines[j])

                        func_impl = {
                            "name": func_name,
                            "file": file_path,
                            "line": i + 1,
                            "signature": stripped,
                            "body_preview": '\n'.join(body_lines),
                            "is_api_endpoint": any(keyword in stripped.lower() for keyword in ['app.', 'router.', 'express', 'fastify', 'request', 'response']),
                            "is_utility": any(keyword in func_name.lower() for keyword in ['util', 'helper', 'format', 'parse', 'validate'])
                        }

                        if func_impl["is_api_endpoint"]:
                            implementations["api_endpoints"].append(func_impl)
                        elif func_impl["is_utility"]:
                            implementations["utilities"].append(func_impl)
                        else:
                            implementations["functions"].append(func_impl)

                # Class definitions
                elif stripped.startswith('class ') and '{' in stripped:
                    class_name = stripped.split('class ')[1].split(' ')[0].split('{')[0].strip()

                    class_impl = {
                        "name": class_name,
                        "file": file_path,
                        "line": i + 1,
                        "signature": stripped,
                        "is_service": any(keyword in class_name.lower() for keyword in ['service', 'manager', 'handler', 'controller']),
                        "is_model": any(keyword in class_name.lower() for keyword in ['model', 'entity', 'schema'])
                    }

                    implementations["classes"].append(class_impl)

        except Exception as e:
            logger.warning(f"Failed to extract JavaScript implementations from {file_path}: {str(e)}")

        return implementations

    def _extract_go_implementations(self, file_path: str, content: str) -> Dict[str, List[Dict[str, Any]]]:
        """Extract Go function implementations"""
        implementations = {"functions": [], "classes": [], "api_endpoints": [], "utilities": []}

        try:
            lines = content.split('\n')

            for i, line in enumerate(lines):
                stripped = line.strip()

                # Function definitions
                if stripped.startswith('func ') and '(' in stripped:
                    func_name = stripped.split('func ')[1].split('(')[0].strip()

                    # Extract function body preview
                    body_lines = []
                    for j in range(i + 1, min(i + 10, len(lines))):
                        if j < len(lines):
                            body_lines.append(lines[j])

                    func_impl = {
                        "name": func_name,
                        "file": file_path,
                        "line": i + 1,
                        "signature": stripped,
                        "body_preview": '\n'.join(body_lines),
                        "is_api_endpoint": any(keyword in stripped.lower() for keyword in ['http', 'handler', 'router', 'gin', 'echo']),
                        "is_utility": any(keyword in func_name.lower() for keyword in ['util', 'helper', 'format', 'parse', 'validate'])
                    }

                    if func_impl["is_api_endpoint"]:
                        implementations["api_endpoints"].append(func_impl)
                    elif func_impl["is_utility"]:
                        implementations["utilities"].append(func_impl)
                    else:
                        implementations["functions"].append(func_impl)

        except Exception as e:
            logger.warning(f"Failed to extract Go implementations from {file_path}: {str(e)}")

        return implementations

    def _extract_java_implementations(self, file_path: str, content: str) -> Dict[str, List[Dict[str, Any]]]:
        """Extract Java method implementations"""
        implementations = {"functions": [], "classes": [], "api_endpoints": [], "utilities": []}

        try:
            lines = content.split('\n')

            for i, line in enumerate(lines):
                stripped = line.strip()

                # Method definitions
                if any(keyword in stripped for keyword in ['public ', 'private ', 'protected ']) and '(' in stripped and not stripped.endswith(';'):
                    # Extract method name
                    parts = stripped.split('(')[0].split()
                    method_name = parts[-1] if parts else ""

                    if method_name and not method_name.startswith('//'):
                        # Extract method body preview
                        body_lines = []
                        for j in range(i + 1, min(i + 10, len(lines))):
                            if j < len(lines):
                                body_lines.append(lines[j])

                        method_impl = {
                            "name": method_name,
                            "file": file_path,
                            "line": i + 1,
                            "signature": stripped,
                            "body_preview": '\n'.join(body_lines),
                            "is_api_endpoint": any(keyword in stripped.lower() for keyword in ['@requestmapping', '@getmapping', '@postmapping', '@controller', '@restcontroller']),
                            "is_utility": any(keyword in method_name.lower() for keyword in ['util', 'helper', 'format', 'parse', 'validate'])
                        }

                        if method_impl["is_api_endpoint"]:
                            implementations["api_endpoints"].append(method_impl)
                        elif method_impl["is_utility"]:
                            implementations["utilities"].append(method_impl)
                        else:
                            implementations["functions"].append(method_impl)

        except Exception as e:
            logger.warning(f"Failed to extract Java implementations from {file_path}: {str(e)}")

        return implementations

    def _extract_generic_implementations(self, file_path: str, content: str) -> Dict[str, List[Dict[str, Any]]]:
        """Generic implementation extraction for other languages"""
        implementations = {"functions": [], "classes": [], "api_endpoints": [], "utilities": []}

        try:
            lines = content.split('\n')

            for i, line in enumerate(lines):
                stripped = line.strip()

                # Look for function-like patterns
                if any(pattern in stripped.lower() for pattern in ['function', 'def ', 'func ', 'method']) and ('(' in stripped or '{' in stripped):
                    # Try to extract a name
                    name = "unknown_function"
                    for pattern in ['function ', 'def ', 'func ']:
                        if pattern in stripped.lower():
                            try:
                                name = stripped.lower().split(pattern)[1].split('(')[0].split(' ')[0].strip()
                                break
                            except:
                                pass

                    if name and name != "unknown_function":
                        func_impl = {
                            "name": name,
                            "file": file_path,
                            "line": i + 1,
                            "signature": stripped,
                            "body_preview": "",
                            "is_api_endpoint": any(keyword in stripped.lower() for keyword in ['http', 'api', 'endpoint', 'route']),
                            "is_utility": any(keyword in name.lower() for keyword in ['util', 'helper', 'format', 'parse'])
                        }

                        implementations["functions"].append(func_impl)

        except Exception as e:
            logger.warning(f"Failed to extract generic implementations from {file_path}: {str(e)}")

        return implementations