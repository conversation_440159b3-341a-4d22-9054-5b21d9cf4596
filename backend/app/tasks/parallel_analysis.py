"""
Parallel Repository Analysis Tasks

Breaks down repository analysis into multiple parallel tasks to utilize all 8 Celery workers.
Each task can run on a different worker process simultaneously.
"""

import asyncio
import logging
from typing import Dict, Any, List
from celery import group, chain, chord
from sqlalchemy.orm import Session

from .celery_app import celery_app
from ..database import SessionLocal
from ..models.analysis import RepoAnalysis
from ..services.intelligent_analysis_service import IntelligentAnalysisService

logger = logging.getLogger(__name__)


@celery_app.task(bind=True, max_retries=2)
def start_parallel_analysis(self, analysis_id: int, github_token: str):
    """
    Orchestrator task that starts parallel analysis workflow
    """
    try:
        # Get analysis info for better logging
        db = SessionLocal()
        analysis = db.query(RepoAnalysis).filter(RepoAnalysis.id == analysis_id).first()
        repo_name = f"{analysis.repository_owner}/{analysis.repository_name}" if analysis else f"analysis_{analysis_id}"
        db.close()

        logger.info(f"🚀 Starting parallel analysis for {repo_name}")
        
        # Step 1: Repository processing (single task - unavoidable)
        repo_task = process_repository_unlimited.s(analysis_id, github_token)
        
        # Step 2: Parallel analysis tasks (multiple workers)
        analysis_tasks = group([
            analyze_business_logic_parallel.s(analysis_id),
            analyze_workflows_parallel.s(analysis_id),
            analyze_apis_parallel.s(analysis_id),
            analyze_integrations_parallel.s(analysis_id),
            index_code_vectors_parallel.s(analysis_id)
        ])
        
        # Step 3: Final aggregation (single task)
        final_task = aggregate_analysis_results.s(analysis_id, github_token)
        
        # Create workflow: repo_processing -> parallel_analysis -> aggregation
        workflow = chain(
            repo_task,
            analysis_tasks,
            final_task
        )
        
        # Execute workflow
        result = workflow.apply_async()
        
        return {
            "status": "parallel_workflow_started",
            "analysis_id": analysis_id,
            "workflow_id": result.id,
            "parallel_tasks": 5
        }
        
    except Exception as e:
        logger.error(f"Failed to start parallel analysis: {e}")
        raise


@celery_app.task(bind=True, max_retries=2)
def process_repository_unlimited(self, analysis_id: int, github_token: str):
    """
    Process repository with unlimited capacity (single task - file processing)
    """
    try:
        logger.info(f"📁 Processing repository for analysis {analysis_id}")
        
        # This part is inherently sequential (git clone + file discovery)
        # But we can optimize it and prepare data for parallel analysis
        
        db = SessionLocal()
        analysis = db.query(RepoAnalysis).filter(RepoAnalysis.id == analysis_id).first()
        
        if not analysis:
            raise Exception(f"Analysis {analysis_id} not found")
        
        # Initialize analysis service
        analysis_service = IntelligentAnalysisService()
        
        # Process repository (this is the bottleneck we can't parallelize)
        repo_url = f"https://github.com/{analysis.repository_owner}/{analysis.repository_name}"
        
        # Use unlimited processing
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        
        try:
            repo_content = loop.run_until_complete(
                analysis_service._analyze_repository_unlimited(
                    repo_url, analysis.repository_owner, analysis.repository_name, 
                    github_token, analysis_id
                )
            )
            
            # Store intermediate results for parallel tasks
            analysis.intermediate_data = repo_content
            db.commit()
            
            logger.info(f"✅ Repository processing completed: {repo_content.get('files_count', 0)} files")
            
            return {
                "status": "repository_processed",
                "analysis_id": analysis_id,
                "files_count": repo_content.get("files_count", 0)
            }
            
        finally:
            loop.close()
            db.close()
            
    except Exception as e:
        logger.error(f"Repository processing failed: {e}")
        raise


@celery_app.task(bind=True, max_retries=2)
def analyze_business_logic_parallel(self, analysis_id: int):
    """
    Analyze business logic in parallel (Worker 1)
    """
    try:
        logger.info(f"🧠 Analyzing business logic for analysis {analysis_id}")
        
        db = SessionLocal()
        analysis = db.query(RepoAnalysis).filter(RepoAnalysis.id == analysis_id).first()

        if not analysis or not analysis.intermediate_data:
            raise Exception(f"No intermediate data for analysis {analysis_id}")
        
        # Initialize analysis service
        analysis_service = IntelligentAnalysisService()
        
        # Run business logic analysis
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        
        try:
            business_analysis = loop.run_until_complete(
                analysis_service._analyze_business_logic(analysis.intermediate_data)
            )
            
            # Store results
            if not analysis.parallel_results:
                analysis.parallel_results = {}
            analysis.parallel_results["business_logic"] = business_analysis
            db.commit()
            
            logger.info(f"✅ Business logic analysis completed for {analysis_id}")
            return {"status": "business_logic_completed", "analysis_id": analysis_id}
            
        finally:
            loop.close()
            db.close()
            
    except Exception as e:
        logger.error(f"Business logic analysis failed: {e}")
        raise


@celery_app.task(bind=True, max_retries=2)
def analyze_workflows_parallel(self, analysis_id: int):
    """
    Analyze workflows in parallel (Worker 2)
    """
    try:
        logger.info(f"🔄 Analyzing workflows for analysis {analysis_id}")
        
        db = SessionLocal()
        analysis = db.query(RepoAnalysis).filter(RepoAnalysis.id == analysis_id).first()

        if not analysis or not analysis.intermediate_data:
            raise Exception(f"No intermediate data for analysis {analysis_id}")

        analysis_service = IntelligentAnalysisService()
        
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        
        try:
            workflow_analysis = loop.run_until_complete(
                analysis_service._analyze_workflows(analysis.intermediate_data)
            )
            
            if not analysis.parallel_results:
                analysis.parallel_results = {}
            analysis.parallel_results["workflows"] = workflow_analysis
            db.commit()
            
            logger.info(f"✅ Workflow analysis completed for {analysis_id}")
            return {"status": "workflows_completed", "analysis_id": analysis_id}
            
        finally:
            loop.close()
            db.close()
            
    except Exception as e:
        logger.error(f"Workflow analysis failed: {e}")
        raise


@celery_app.task(bind=True, max_retries=2)
def analyze_apis_parallel(self, analysis_id: int):
    """
    Analyze APIs in parallel (Worker 3)
    """
    try:
        logger.info(f"🌐 Analyzing APIs for analysis {analysis_id}")
        
        db = SessionLocal()
        analysis = db.query(RepoAnalysis).filter(RepoAnalysis.id == analysis_id).first()

        if not analysis or not analysis.intermediate_data:
            raise Exception(f"No intermediate data for analysis {analysis_id}")

        analysis_service = IntelligentAnalysisService()
        
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        
        try:
            api_analysis = loop.run_until_complete(
                analysis_service._analyze_apis(analysis.intermediate_data)
            )
            
            if not analysis.parallel_results:
                analysis.parallel_results = {}
            analysis.parallel_results["apis"] = api_analysis
            db.commit()
            
            logger.info(f"✅ API analysis completed for {analysis_id}")
            return {"status": "apis_completed", "analysis_id": analysis_id}
            
        finally:
            loop.close()
            db.close()
            
    except Exception as e:
        logger.error(f"API analysis failed: {e}")
        raise


@celery_app.task(bind=True, max_retries=2)
def analyze_integrations_parallel(self, analysis_id: int):
    """
    Analyze integrations in parallel (Worker 4)
    """
    try:
        logger.info(f"🔗 Analyzing integrations for analysis {analysis_id}")
        
        db = SessionLocal()
        analysis = db.query(RepoAnalysis).filter(RepoAnalysis.id == analysis_id).first()

        if not analysis or not analysis.intermediate_data:
            raise Exception(f"No intermediate data for analysis {analysis_id}")

        analysis_service = IntelligentAnalysisService()
        
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        
        try:
            integration_analysis = loop.run_until_complete(
                analysis_service._analyze_integration_points(analysis.intermediate_data)
            )
            
            if not analysis.parallel_results:
                analysis.parallel_results = {}
            analysis.parallel_results["integrations"] = integration_analysis
            db.commit()
            
            logger.info(f"✅ Integration analysis completed for {analysis_id}")
            return {"status": "integrations_completed", "analysis_id": analysis_id}
            
        finally:
            loop.close()
            db.close()
            
    except Exception as e:
        logger.error(f"Integration analysis failed: {e}")
        raise


@celery_app.task(bind=True, max_retries=2)
def index_code_vectors_parallel(self, analysis_id: int):
    """
    Index code vectors in parallel (Worker 5)
    """
    try:
        logger.info(f"🔍 Indexing code vectors for analysis {analysis_id}")
        
        db = SessionLocal()
        analysis = db.query(RepoAnalysis).filter(RepoAnalysis.id == analysis_id).first()

        if not analysis or not analysis.intermediate_data:
            raise Exception(f"No intermediate data for analysis {analysis_id}")

        analysis_service = IntelligentAnalysisService()
        
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        
        try:
            # Initialize vector service
            if not analysis_service.vector_service:
                from ..services.unified_vector_service import UnifiedVectorService
                analysis_service.vector_service = UnifiedVectorService()
                loop.run_until_complete(analysis_service.vector_service.__aenter__())

            # Index vectors
            vector_result = loop.run_until_complete(
                analysis_service.vector_service.index_repository_code(analysis_id, analysis.intermediate_data)
            )
            
            if not analysis.parallel_results:
                analysis.parallel_results = {}
            analysis.parallel_results["vector_indexing"] = vector_result
            db.commit()
            
            logger.info(f"✅ Vector indexing completed for {analysis_id}")
            return {"status": "vectors_completed", "analysis_id": analysis_id}
            
        finally:
            loop.close()
            db.close()
            
    except Exception as e:
        logger.error(f"Vector indexing failed: {e}")
        raise


@celery_app.task(bind=True, max_retries=2)
def aggregate_analysis_results(self, analysis_id: int, github_token: str, parallel_results: List[Dict]):
    """
    Aggregate all parallel analysis results and generate final MCP suggestions
    """
    try:
        logger.info(f"📊 Aggregating analysis results for {analysis_id}")
        
        db = SessionLocal()
        analysis = db.query(RepoAnalysis).filter(RepoAnalysis.id == analysis_id).first()

        if not analysis:
            raise Exception(f"Analysis {analysis_id} not found")
        
        # Combine all parallel results
        combined_results = {
            "repository_info": analysis.intermediate_data.get("repository_info", {}),
            "business_logic": analysis.parallel_results.get("business_logic", {}),
            "workflows": analysis.parallel_results.get("workflows", {}),
            "apis": analysis.parallel_results.get("apis", {}),
            "integrations": analysis.parallel_results.get("integrations", {}),
            "vector_indexing": analysis.parallel_results.get("vector_indexing", {}),
            "code_samples": analysis.intermediate_data.get("code_samples", {})
        }
        
        # Generate MCP suggestions
        analysis_service = IntelligentAnalysisService()
        
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        
        try:
            # Generate MCP suggestions with combined data
            code_samples_sample = dict(list(combined_results["code_samples"].items())[:20])
            
            mcp_suggestions = loop.run_until_complete(
                analysis_service._generate_mcp_suggestions({
                    "repository_info": combined_results["repository_info"],
                    "business_logic": combined_results["business_logic"],
                    "workflows": combined_results["workflows"],
                    "apis": combined_results["apis"],
                    "integrations": combined_results["integrations"],
                    "code_samples": code_samples_sample,
                    "total_files_processed": analysis.intermediate_data.get("files_count", 0)
                })
            )
            
            # Store final results
            analysis.analysis_result = combined_results
            analysis.mcp_suggestions = mcp_suggestions
            analysis.status = "completed"
            db.commit()
            
            logger.info(f"✅ Parallel analysis completed for {analysis_id}")
            
            return {
                "status": "completed",
                "analysis_id": analysis_id,
                "parallel_workers_used": 5,
                "files_processed": analysis.intermediate_data.get("files_count", 0)
            }
            
        finally:
            loop.close()
            db.close()
            
    except Exception as e:
        logger.error(f"Analysis aggregation failed: {e}")
        raise
